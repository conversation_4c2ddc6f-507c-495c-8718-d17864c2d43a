#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create placeholder images for the Inflamed app
This creates basic placeholder images until the actual assets are provided
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_splash_background():
    """Create a placeholder splash background image"""
    # Create image with medical/healthcare theme
    width, height = 1080, 1920
    img = Image.new('RGB', (width, height), color='#F5F5F5')
    draw = ImageDraw.Draw(img)
    
    # Add a subtle gradient effect
    for y in range(height):
        alpha = int(255 * (y / height) * 0.1)
        color = (245 - alpha, 245 - alpha, 245 - alpha)
        draw.line([(0, y), (width, y)], fill=color)
    
    # Add some subtle medical-themed elements
    # Draw some circles to represent medical/health theme
    for i in range(5):
        x = width // 4 + (i * width // 8)
        y = height // 3 + (i * 50)
        radius = 30 + (i * 10)
        draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                    outline=(200, 200, 200, 50), width=2)
    
    # Add a phone mockup silhouette in the center
    phone_width, phone_height = 300, 600
    phone_x = (width - phone_width) // 2
    phone_y = (height - phone_height) // 2
    
    # Phone outline
    draw.rounded_rectangle([phone_x, phone_y, phone_x + phone_width, phone_y + phone_height],
                          radius=30, outline=(180, 180, 180), width=3)
    
    # Phone screen
    screen_margin = 20
    draw.rounded_rectangle([phone_x + screen_margin, phone_y + screen_margin, 
                           phone_x + phone_width - screen_margin, phone_y + phone_height - screen_margin],
                          radius=20, fill=(240, 240, 240))
    
    return img

def create_continua_logo():
    """Create a placeholder Continua logo"""
    size = 400
    img = Image.new('RGBA', (size, size), color=(0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Draw dotted circle
    center = size // 2
    radius = 180
    dot_count = 24
    dot_radius = 12
    
    import math
    for i in range(dot_count):
        angle = (i * 2 * math.pi) / dot_count
        x = center + radius * math.cos(angle)
        y = center + radius * math.sin(angle)
        
        # Varying opacity for gradient effect
        opacity = int(100 + (math.sin(angle + math.pi/4) + 1) * 77.5)
        color = (255, 255, 255, opacity)
        
        draw.ellipse([x - dot_radius, y - dot_radius, x + dot_radius, y + dot_radius], 
                    fill=color)
    
    # Draw center circle with plus
    center_radius = 50
    draw.ellipse([center - center_radius, center - center_radius, 
                 center + center_radius, center + center_radius], 
                fill=(255, 255, 255, 255))
    
    # Draw plus sign
    plus_size = 30
    plus_width = 6
    # Horizontal line
    draw.rectangle([center - plus_size//2, center - plus_width//2,
                   center + plus_size//2, center + plus_width//2],
                  fill=(44, 14, 87, 255))  # AppColors.primary
    # Vertical line
    draw.rectangle([center - plus_width//2, center - plus_size//2,
                   center + plus_width//2, center + plus_size//2],
                  fill=(44, 14, 87, 255))  # AppColors.primary
    
    return img

def main():
    """Create all placeholder images"""
    assets_dir = "assets/images"
    
    # Create assets directory if it doesn't exist
    os.makedirs(assets_dir, exist_ok=True)
    
    print("Creating placeholder images...")
    
    # Create splash background
    splash_bg = create_splash_background()
    splash_bg.save(f"{assets_dir}/splash_background.png", "PNG")
    print("✓ Created splash_background.png")
    
    # Create Continua logo
    continua_logo = create_continua_logo()
    continua_logo.save(f"{assets_dir}/continua_logo.png", "PNG")
    print("✓ Created continua_logo.png")
    
    print("\nPlaceholder images created successfully!")
    print("Replace these with your actual images when available.")

if __name__ == "__main__":
    main()
