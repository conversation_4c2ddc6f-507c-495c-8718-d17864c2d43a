// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:inflamed/main.dart';

void main() {
  testWidgets('App loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const InflamedApp());

    // Verify that the app loads with the welcome message.
    expect(find.text('Welcome to Inflamed'), findsOneWidget);
    expect(find.text('Your Health Companion'), findsOneWidget);

    // Verify that the color showcase button is present.
    expect(find.text('View Color Showcase'), findsOneWidget);

    // Verify that theme switching menu is present.
    expect(find.byIcon(Icons.light_mode), findsOneWidget);
  });
}
