import 'package:flutter/material.dart';

/// App color constants converted from iOS AppThemeColors
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // MARK: - Universal Colors
  /// Universal accent color - RGB(204, 173, 116) / RGB(204, 173, 116)
  static const Color universalAccent = Color.fromRGBO(204, 173, 116, 1.0);
  
  /// Tab accent color
  static const Color tabAccent = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color tabAccentDark = Color.fromRGBO(237, 223, 182, 1.0);
  
  /// Themed border color
  static const Color themedBorder = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color themedBorderDark = Color.fromRGBO(237, 223, 182, 1.0);
  
  /// Shadow color
  static const Color shadow = Color.fromRGBO(0, 0, 0, 0.25);
  static const Color shadowDark = Color.fromRGBO(0, 0, 0, 0.5);

  // MARK: - Background Colors
  /// Primary background - Light: RGB(245, 245, 245), Dark: RGB(0, 0, 0)
  static const Color primaryBackground = Color.fromRGBO(245, 245, 245, 1.0);
  static const Color primaryBackgroundDark = Color.fromRGBO(0, 0, 0, 1.0);
  
  /// Secondary background
  static const Color secondaryBackground = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color secondaryBackgroundDark = Color.fromRGBO(28, 28, 30, 1.0);
  
  /// Onboarding tutorial background - Light: RGB(240, 240, 240), Dark: RGB(27, 22, 33)
  static const Color onboardingBackground = Color.fromRGBO(240, 240, 240, 1.0);
  static const Color onboardingBackgroundDark = Color.fromRGBO(27, 22, 33, 1.0);
  
  /// Splash screen gradient colors
  static const Color splashGradientTopLeft = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color splashGradientBottomRight = Color.fromRGBO(42, 34, 53, 1.0);
  
  /// Air quality record background
  static const Color airQualityBackground = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color airQualityBackgroundDark = Color.fromRGBO(28, 28, 30, 1.0);
  
  /// Recent notification background
  static const Color recentNotiBackground = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color recentNotiBackgroundDark = Color.fromRGBO(28, 28, 30, 1.0);
  
  /// High contrast text background
  static const Color highContrastTextBackground = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color highContrastTextBackgroundDark = Color.fromRGBO(0, 0, 0, 1.0);

  // MARK: - Text Colors
  /// Primary text - Light: RGB(24, 24, 24), Dark: RGB(255, 255, 255)
  static const Color primaryText = Color.fromRGBO(24, 24, 24, 1.0);
  static const Color primaryTextDark = Color.fromRGBO(255, 255, 255, 1.0);
  
  /// Secondary text
  static const Color secondaryText = Color.fromRGBO(142, 142, 147, 1.0);
  static const Color secondaryTextDark = Color.fromRGBO(174, 174, 178, 1.0);
  
  /// Primary subtitle text
  static const Color primarySubtitleText = Color.fromRGBO(142, 142, 147, 1.0);
  static const Color primarySubtitleTextDark = Color.fromRGBO(174, 174, 178, 1.0);
  
  /// Onboarding tutorial text
  static const Color onboardingTutorialText = Color.fromRGBO(60, 60, 67, 1.0);
  static const Color onboardingTutorialTextDark = Color.fromRGBO(235, 235, 245, 1.0);
  
  /// Urgent color
  static const Color urgent = Color.fromRGBO(255, 59, 48, 1.0);
  static const Color urgentDark = Color.fromRGBO(255, 69, 58, 1.0);

  // MARK: - Button Colors
  /// Primary button background - Light: RGB(44, 14, 87), Dark: RGB(230, 221, 234)
  static const Color primaryButtonBackground = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color primaryButtonBackgroundDark = Color.fromRGBO(230, 221, 234, 1.0);
  
  /// Secondary button background
  static const Color secondaryButtonBackground = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color secondaryButtonBackgroundDark = Color.fromRGBO(28, 28, 30, 1.0);
  
  /// Tertiary button background
  static const Color tertiaryButtonBackground = Color.fromRGBO(0, 0, 0, 0.0);
  static const Color tertiaryButtonBackgroundDark = Color.fromRGBO(0, 0, 0, 0.0);
  
  /// Onboarding button background - RGB(237, 223, 182)
  static const Color onboardingButtonBackground = Color.fromRGBO(237, 223, 182, 1.0);

  /// Text guide button color - RGB(255, 191, 198)
  static const Color textGuideButtonColor = Color.fromRGBO(255, 191, 198, 1.0);
  
  /// Action required button background
  static const Color actionRequiredButtonBackground = Color.fromRGBO(255, 59, 48, 1.0);
  static const Color actionRequiredButtonBackgroundDark = Color.fromRGBO(255, 69, 58, 1.0);
  
  /// Next appointment button background
  static const Color nextAppointmentButtonBackground = Color.fromRGBO(52, 199, 89, 1.0);
  static const Color nextAppointmentButtonBackgroundDark = Color.fromRGBO(48, 209, 88, 1.0);

  // MARK: - Button Text Colors
  /// Primary button text
  static const Color primaryButtonText = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color primaryButtonTextDark = Color.fromRGBO(44, 14, 87, 1.0);
  
  /// Secondary button text
  static const Color secondaryButtonText = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color secondaryButtonTextDark = Color.fromRGBO(255, 255, 255, 1.0);
  
  /// Tertiary button text
  static const Color tertiaryButtonText = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color tertiaryButtonTextDark = Color.fromRGBO(237, 223, 182, 1.0);
  
  /// Onboarding button text
  static const Color onboardingButtonText = Color.fromRGBO(44, 14, 87, 1.0);

  // MARK: - Link Text Colors
  /// Primary link text
  static const Color primaryLinkText = Color.fromRGBO(0, 122, 255, 1.0);
  static const Color primaryLinkTextDark = Color.fromRGBO(10, 132, 255, 1.0);
  
  /// Secondary link text
  static const Color secondaryLinkText = Color.fromRGBO(88, 86, 214, 1.0);
  static const Color secondaryLinkTextDark = Color.fromRGBO(120, 120, 255, 1.0);
  
  /// Onboarding link text
  static const Color onboardingLinkText = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color onboardingLinkTextDark = Color.fromRGBO(237, 223, 182, 1.0);
  
  /// Universal link pink color
  static const Color universalLinkPink = Color.fromRGBO(255, 45, 85, 1.0);
  static const Color universalLinkPinkDark = Color.fromRGBO(255, 55, 95, 1.0);

  // MARK: - Text Field Colors
  /// Primary text field background
  static const Color primaryTextFieldBackground = Color.fromRGBO(255, 255, 255, 1.0);
  static const Color primaryTextFieldBackgroundDark = Color.fromRGBO(28, 28, 30, 1.0);
  
  /// Secondary text field background
  static const Color secondaryTextFieldBackground = Color.fromRGBO(242, 242, 247, 1.0);
  static const Color secondaryTextFieldBackgroundDark = Color.fromRGBO(44, 44, 46, 1.0);
  
  /// Primary text field text
  static const Color primaryTextFieldText = Color.fromRGBO(24, 24, 24, 1.0);
  static const Color primaryTextFieldTextDark = Color.fromRGBO(255, 255, 255, 1.0);
  
  /// Secondary text field text
  static const Color secondaryTextFieldText = Color.fromRGBO(142, 142, 147, 1.0);
  static const Color secondaryTextFieldTextDark = Color.fromRGBO(174, 174, 178, 1.0);

  // MARK: - Body Severity Colors
  /// Severity None - Green
  static const Color severityNone = Color.fromRGBO(52, 199, 89, 1.0);
  static const Color severityNoneDark = Color.fromRGBO(48, 209, 88, 1.0);

  /// Severity Mild - Yellow
  static const Color severityMild = Color.fromRGBO(255, 204, 0, 1.0);
  static const Color severityMildDark = Color.fromRGBO(255, 214, 10, 1.0);

  /// Severity Moderate - Orange
  static const Color severityModerate = Color.fromRGBO(255, 149, 0, 1.0);
  static const Color severityModerateDark = Color.fromRGBO(255, 159, 10, 1.0);

  /// Severity Severe - Red
  static const Color severitySevere = Color.fromRGBO(255, 59, 48, 1.0);
  static const Color severitySevereDark = Color.fromRGBO(255, 69, 58, 1.0);

  // MARK: - Air Quality Index Colors
  /// AQI Good - Green
  static const Color aqiGood = Color.fromRGBO(52, 199, 89, 1.0);
  static const Color aqiGoodDark = Color.fromRGBO(48, 209, 88, 1.0);

  /// AQI Moderate - Yellow
  static const Color aqiModerate = Color.fromRGBO(255, 204, 0, 1.0);
  static const Color aqiModerateDark = Color.fromRGBO(255, 214, 10, 1.0);

  /// AQI Unhealthy for Sensitive Groups - Orange
  static const Color aqiUnhealthyForSensitive = Color.fromRGBO(255, 149, 0, 1.0);
  static const Color aqiUnhealthyForSensitiveDark = Color.fromRGBO(255, 159, 10, 1.0);

  /// AQI Unhealthy - Red
  static const Color aqiUnhealthy = Color.fromRGBO(255, 59, 48, 1.0);
  static const Color aqiUnhealthyDark = Color.fromRGBO(255, 69, 58, 1.0);

  /// AQI Very Unhealthy - Purple
  static const Color aqiVeryUnhealthy = Color.fromRGBO(175, 82, 222, 1.0);
  static const Color aqiVeryUnhealthyDark = Color.fromRGBO(191, 90, 242, 1.0);

  /// AQI Hazardous - Maroon
  static const Color aqiHazardous = Color.fromRGBO(162, 132, 94, 1.0);
  static const Color aqiHazardousDark = Color.fromRGBO(172, 142, 104, 1.0);

  // MARK: - Chart Category Colors
  /// Happiness chart color - Pink
  static const Color chartHappiness = Color.fromRGBO(255, 45, 85, 1.0);
  static const Color chartHappinessDark = Color.fromRGBO(255, 55, 95, 1.0);

  /// HRV (Heart Rate Variability) chart color - Red
  static const Color chartHRV = Color.fromRGBO(255, 59, 48, 1.0);
  static const Color chartHRVDark = Color.fromRGBO(255, 69, 58, 1.0);

  /// Sentiment chart color - Blue
  static const Color chartSentiment = Color.fromRGBO(0, 122, 255, 1.0);
  static const Color chartSentimentDark = Color.fromRGBO(10, 132, 255, 1.0);

  /// Sleep chart color - Purple
  static const Color chartSleep = Color.fromRGBO(88, 86, 214, 1.0);
  static const Color chartSleepDark = Color.fromRGBO(120, 120, 255, 1.0);

  /// Steps chart color - Green
  static const Color chartSteps = Color.fromRGBO(52, 199, 89, 1.0);
  static const Color chartStepsDark = Color.fromRGBO(48, 209, 88, 1.0);

  /// Stress chart color - Orange
  static const Color chartStress = Color.fromRGBO(255, 149, 0, 1.0);
  static const Color chartStressDark = Color.fromRGBO(255, 159, 10, 1.0);

  // MARK: - Semantic Colors (Convenience getters)
  /// Primary brand color
  static const Color primary = Color.fromRGBO(44, 14, 87, 1.0);
  static const Color primaryDark = Color.fromRGBO(237, 223, 182, 1.0);

  /// Secondary brand color
  static const Color secondary = Color.fromRGBO(237, 223, 182, 1.0);
  static const Color secondaryDark = Color.fromRGBO(44, 14, 87, 1.0);

  /// Success color
  static const Color success = Color.fromRGBO(52, 199, 89, 1.0);
  static const Color successDark = Color.fromRGBO(48, 209, 88, 1.0);

  /// Warning color
  static const Color warning = Color.fromRGBO(255, 204, 0, 1.0);
  static const Color warningDark = Color.fromRGBO(255, 214, 10, 1.0);

  /// Error color
  static const Color error = Color.fromRGBO(255, 59, 48, 1.0);
  static const Color errorDark = Color.fromRGBO(255, 69, 58, 1.0);

  /// Info color
  static const Color info = Color.fromRGBO(0, 122, 255, 1.0);
  static const Color infoDark = Color.fromRGBO(10, 132, 255, 1.0);
}
