/// App-wide constants for the Inflamed medical app
class AppConstants {
  // Private constructor to prevent instantiation
  AppConstants._();

  // MARK: - App Information
  static const String appName = 'Inflamed';
  static const String appTagline = 'Your Health Companion';
  static const String appVersion = '1.0.0';

  // MARK: - Animation Durations
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration pageTransitionDuration = Duration(milliseconds: 250);
  static const Duration fadeAnimationDuration = Duration(milliseconds: 500);

  // MARK: - Spacing and Sizing
  static const double paddingXSmall = 4.0;
  static const double paddingSmall = 8.0;
  static const double paddingMedium = 16.0;
  static const double paddingLarge = 24.0;
  static const double paddingXLarge = 32.0;
  static const double paddingXXLarge = 48.0;

  static const double marginSmall = 8.0;
  static const double marginMedium = 16.0;
  static const double marginLarge = 24.0;

  static const double borderRadius = 12.0;
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusLarge = 20.0;
  static const double borderRadiusXLarge = 28.0;

  static const double buttonHeight = 56.0;
  static const double buttonHeightSmall = 44.0;
  static const double buttonHeightLarge = 64.0;

  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  // MARK: - Screen Breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 1024.0;
  static const double desktopBreakpoint = 1440.0;

  // MARK: - Asset Paths
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String fontsPath = 'assets/fonts/';

  // MARK: - Image Assets
  static const String splashBackground = '${imagesPath}splash_background.png';
  static const String onboardingBackground1 = '${imagesPath}onboarding_bg_1.png';
  static const String onboardingBackground2 = '${imagesPath}onboarding_bg_2.png';
  static const String onboardingBackground3 = '${imagesPath}onboarding_bg_3.png';
  static const String videoTutorialBackground = '${imagesPath}video_tutorial_bg.png';
  static const String appLogo = '${imagesPath}app_logo.png';
  static const String doctorIllustration = '${imagesPath}doctor_illustration.png';
  static const String patientIllustration = '${imagesPath}patient_illustration.png';
  static const String medicineIllustration = '${imagesPath}medicine_illustration.png';

  // MARK: - Onboarding Content
  static const String onboarding1Title = 'Welcome to Inflamed';
  static const String onboarding1Subtitle = 'Your Personal Health Companion';
  static const String onboarding1Description = 
      'Track your health metrics, monitor symptoms, and get personalized insights '
      'to help you manage inflammation and improve your overall well-being.';

  static const String onboarding2Title = 'Monitor Your Health';
  static const String onboarding2Subtitle = 'Real-time Health Tracking';
  static const String onboarding2Description = 
      'Keep track of your daily symptoms, medication intake, and vital signs. '
      'Our intelligent system helps you identify patterns and triggers.';

  static const String onboarding3Title = 'Get Personalized Insights';
  static const String onboarding3Subtitle = 'AI-Powered Health Analysis';
  static const String onboarding3Description = 
      'Receive personalized recommendations based on your health data. '
      'Connect with healthcare professionals and access educational resources.';

  static const String videoTutorialTitle = 'How to Use Inflamed';
  static const String videoTutorialSubtitle = 'Quick Start Guide';
  static const String videoTutorialDescription = 
      'Watch this quick tutorial to learn how to make the most of your Inflamed app '
      'and start your journey to better health management.';

  // MARK: - Button Texts
  static const String getStarted = 'Get Started';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String skip = 'Skip';
  static const String done = 'Done';
  static const String continueText = 'Continue';
  static const String cancel = 'Cancel';
  static const String save = 'Save';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String add = 'Add';
  static const String remove = 'Remove';
  static const String confirm = 'Confirm';
  static const String retry = 'Retry';
  static const String refresh = 'Refresh';
  static const String loadMore = 'Load More';

  // MARK: - Navigation Labels
  static const String home = 'Home';
  static const String dashboard = 'Dashboard';
  static const String symptoms = 'Symptoms';
  static const String medications = 'Medications';
  static const String reports = 'Reports';
  static const String settings = 'Settings';
  static const String profile = 'Profile';
  static const String help = 'Help';
  static const String about = 'About';

  // MARK: - Video Tutorial Options
  static const String watchVideoTutorial = 'Watch Video Tutorial';
  static const String readTextGuide = 'Read Text Guide';
  static const String skipTutorial = 'Skip Tutorial';

  // MARK: - Error Messages
  static const String genericError = 'Something went wrong. Please try again.';
  static const String networkError = 'Please check your internet connection.';
  static const String timeoutError = 'Request timed out. Please try again.';
  static const String validationError = 'Please check your input and try again.';
  static const String permissionError = 'Permission denied. Please check app settings.';

  // MARK: - Success Messages
  static const String dataSaved = 'Data saved successfully';
  static const String profileUpdated = 'Profile updated successfully';
  static const String settingsSaved = 'Settings saved successfully';

  // MARK: - Loading Messages
  static const String loading = 'Loading...';
  static const String saving = 'Saving...';
  static const String processing = 'Processing...';
  static const String uploading = 'Uploading...';
  static const String downloading = 'Downloading...';

  // MARK: - SharedPreferences Keys
  static const String keyFirstLaunch = 'first_launch';
  static const String keyOnboardingCompleted = 'onboarding_completed';
  static const String keyThemeMode = 'theme_mode';
  static const String keyUserProfile = 'user_profile';
  static const String keyAppSettings = 'app_settings';
  static const String keyLastSyncTime = 'last_sync_time';

  // MARK: - API Configuration
  static const String baseUrl = 'https://api.inflamed.app';
  static const String apiVersion = 'v1';
  static const Duration apiTimeout = Duration(seconds: 30);

  // MARK: - Feature Flags
  static const bool enableVideoTutorials = true;
  static const bool enableDarkMode = true;
  static const bool enableNotifications = true;
  static const bool enableAnalytics = false;
  static const bool enableCrashReporting = false;

  // MARK: - Validation Rules
  static const int minPasswordLength = 8;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB

  // MARK: - Chart Configuration
  static const int maxDataPoints = 100;
  static const double chartHeight = 200.0;
  static const double chartPadding = 16.0;

  // MARK: - Notification Configuration
  static const String notificationChannelId = 'inflamed_notifications';
  static const String notificationChannelName = 'Inflamed Notifications';
  static const String notificationChannelDescription = 'Health reminders and updates';
}
