import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

/// Demo widget to showcase all converted colors from iOS AppThemeColors
class ColorShowcase extends StatelessWidget {
  const ColorShowcase({super.key});

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Color Showcase'),
        actions: [
          IconButton(
            icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
            onPressed: () {
              // Theme toggle would be implemented here
            },
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildColorSection(
            'Universal Colors',
            [
              _ColorItem('Universal Accent', AppColors.universalAccent),
              _ColorItem('Tab Accent', isDark ? AppColors.tabAccentDark : AppColors.tabAccent),
              _ColorItem('Themed Border', isDark ? AppColors.themedBorderDark : AppColors.themedBorder),
              _ColorItem('Shadow', isDark ? AppColors.shadowDark : AppColors.shadow),
            ],
          ),
          
          _buildColorSection(
            'Background Colors',
            [
              _ColorItem('Primary Background', isDark ? AppColors.primaryBackgroundDark : AppColors.primaryBackground),
              _ColorItem('Secondary Background', isDark ? AppColors.secondaryBackgroundDark : AppColors.secondaryBackground),
              _ColorItem('Onboarding Background', isDark ? AppColors.onboardingBackgroundDark : AppColors.onboardingBackground),
              _ColorItem('Splash Gradient Top Left', AppColors.splashGradientTopLeft),
              _ColorItem('Splash Gradient Bottom Right', AppColors.splashGradientBottomRight),
            ],
          ),
          
          _buildColorSection(
            'Text Colors',
            [
              _ColorItem('Primary Text', isDark ? AppColors.primaryTextDark : AppColors.primaryText),
              _ColorItem('Secondary Text', isDark ? AppColors.secondaryTextDark : AppColors.secondaryText),
              _ColorItem('Onboarding Tutorial Text', isDark ? AppColors.onboardingTutorialTextDark : AppColors.onboardingTutorialText),
              _ColorItem('Urgent Color', isDark ? AppColors.urgentDark : AppColors.urgent),
            ],
          ),
          
          _buildColorSection(
            'Button Colors',
            [
              _ColorItem('Primary Button Background', isDark ? AppColors.primaryButtonBackgroundDark : AppColors.primaryButtonBackground),
              _ColorItem('Secondary Button Background', isDark ? AppColors.secondaryButtonBackgroundDark : AppColors.secondaryButtonBackground),
              _ColorItem('Onboarding Button Background', AppColors.onboardingButtonBackground),
              _ColorItem('Action Required Button', isDark ? AppColors.actionRequiredButtonBackgroundDark : AppColors.actionRequiredButtonBackground),
            ],
          ),
          
          _buildColorSection(
            'Body Severity Colors',
            [
              _ColorItem('Severity None', isDark ? AppColors.severityNoneDark : AppColors.severityNone),
              _ColorItem('Severity Mild', isDark ? AppColors.severityMildDark : AppColors.severityMild),
              _ColorItem('Severity Moderate', isDark ? AppColors.severityModerateDark : AppColors.severityModerate),
              _ColorItem('Severity Severe', isDark ? AppColors.severitySevereDark : AppColors.severitySevere),
            ],
          ),
          
          _buildColorSection(
            'Air Quality Index Colors',
            [
              _ColorItem('AQI Good', isDark ? AppColors.aqiGoodDark : AppColors.aqiGood),
              _ColorItem('AQI Moderate', isDark ? AppColors.aqiModerateDark : AppColors.aqiModerate),
              _ColorItem('AQI Unhealthy for Sensitive', isDark ? AppColors.aqiUnhealthyForSensitiveDark : AppColors.aqiUnhealthyForSensitive),
              _ColorItem('AQI Unhealthy', isDark ? AppColors.aqiUnhealthyDark : AppColors.aqiUnhealthy),
              _ColorItem('AQI Very Unhealthy', isDark ? AppColors.aqiVeryUnhealthyDark : AppColors.aqiVeryUnhealthy),
              _ColorItem('AQI Hazardous', isDark ? AppColors.aqiHazardousDark : AppColors.aqiHazardous),
            ],
          ),
          
          _buildColorSection(
            'Chart Category Colors',
            [
              _ColorItem('Chart Happiness', isDark ? AppColors.chartHappinessDark : AppColors.chartHappiness),
              _ColorItem('Chart HRV', isDark ? AppColors.chartHRVDark : AppColors.chartHRV),
              _ColorItem('Chart Sentiment', isDark ? AppColors.chartSentimentDark : AppColors.chartSentiment),
              _ColorItem('Chart Sleep', isDark ? AppColors.chartSleepDark : AppColors.chartSleep),
              _ColorItem('Chart Steps', isDark ? AppColors.chartStepsDark : AppColors.chartSteps),
              _ColorItem('Chart Stress', isDark ? AppColors.chartStressDark : AppColors.chartStress),
            ],
          ),
          
          _buildColorSection(
            'Semantic Colors',
            [
              _ColorItem('Primary', isDark ? AppColors.primaryDark : AppColors.primary),
              _ColorItem('Secondary', isDark ? AppColors.secondaryDark : AppColors.secondary),
              _ColorItem('Success', isDark ? AppColors.successDark : AppColors.success),
              _ColorItem('Warning', isDark ? AppColors.warningDark : AppColors.warning),
              _ColorItem('Error', isDark ? AppColors.errorDark : AppColors.error),
              _ColorItem('Info', isDark ? AppColors.infoDark : AppColors.info),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildColorSection(String title, List<_ColorItem> colors) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: colors.length,
          itemBuilder: (context, index) {
            final colorItem = colors[index];
            return Container(
              decoration: BoxDecoration(
                color: colorItem.color,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.grey.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Center(
                child: Text(
                  colorItem.name,
                  style: TextStyle(
                    color: _getContrastColor(colorItem.color),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Color _getContrastColor(Color color) {
    // Calculate luminance to determine if text should be black or white
    final luminance = color.computeLuminance();
    return luminance > 0.5 ? Colors.black : Colors.white;
  }
}

class _ColorItem {
  final String name;
  final Color color;

  _ColorItem(this.name, this.color);
}
