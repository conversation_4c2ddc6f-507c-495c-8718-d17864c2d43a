import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_colors.dart';

class VideoTutorialPage extends StatefulWidget {
  const VideoTutorialPage({super.key});

  @override
  State<VideoTutorialPage> createState() => _VideoTutorialPageState();
}

class _VideoTutorialPageState extends State<VideoTutorialPage> {
  @override
  void initState() {
    super.initState();

    // Set status bar to transparent with light icons
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: SizedBox(
        width: size.width,
        height: size.height,
        child: Stack(
          children: [
            // Background image with purple overlay (similar to splash screen)
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage('assets/images/SplashScreens/landingBackground.png'),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppColors.splashGradientTopLeft.withValues(alpha: 0.7),
                        AppColors.splashGradientBottomRight.withValues(alpha: 0.8),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // Content overlay
            SafeArea(
              child: Column(
                children: [
                  // Top section with play button and text
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(24.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 60),

                          // Play button and text row
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              // Play button
                              GestureDetector(
                                onTap: () {
                                  _playVideo();
                                },
                                child: Container(
                                  width: 60,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: AppColors.onboardingButtonBackground,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.black.withValues(alpha: 0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.play_arrow,
                                    size: 30,
                                    color: AppColors.primary,
                                  ),
                                ),
                              ),

                              const SizedBox(width: 16),

                              // Text content
                              Expanded(
                                child: Text(
                                  'Lorem Ipsum is\nsimply dummy text.',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w500,
                                    color: Colors.white,
                                    height: 1.3,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Bottom buttons
                  Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      children: [
                        // GET STARTED Button
                        SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: ElevatedButton(
                            onPressed: () {
                              _getStarted();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.onboardingButtonBackground,
                              foregroundColor: AppColors.primary,
                              elevation: 0,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(28),
                              ),
                            ),
                            child: const Text(
                              'GET STARTED',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                letterSpacing: 1.0,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Skip Button
                        TextButton(
                          onPressed: () {
                            _skipToHome();
                          },
                          child: Text(
                            'Skip',
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.textGuideButtonColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _playVideo() {
    // Implement video playback
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Video playback would start here'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _getStarted() {
    // Navigate to main app or next onboarding step
    Navigator.pushReplacementNamed(context, '/home');
  }

  void _skipToHome() {
    // Skip to main app
    Navigator.pushReplacementNamed(context, '/home');
  }
}
