import '../../domain/entities/auth_request.dart';

/// Login request model for data layer
class LoginRequestModel extends LoginRequest {
  const LoginRequestModel({
    required super.email,
    required super.password,
  });

  /// Create from JSON
  factory LoginRequestModel.fromJson(Map<String, dynamic> json) {
    return LoginRequestModel(
      email: json['email'] as String,
      password: json['password'] as String,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
    };
  }

  /// Create from entity
  factory LoginRequestModel.fromEntity(LoginRequest request) {
    return LoginRequestModel(
      email: request.email,
      password: request.password,
    );
  }
}

/// Register request model for data layer
class RegisterRequestModel extends RegisterRequest {
  const RegisterRequestModel({
    required super.email,
    required super.password,
    required super.confirmPassword,
    required super.agreeToTerms,
    super.firstName,
    super.lastName,
  });

  /// Create from JSON
  factory RegisterRequestModel.fromJson(Map<String, dynamic> json) {
    return RegisterRequestModel(
      email: json['email'] as String,
      password: json['password'] as String,
      confirmPassword: json['confirmPassword'] as String,
      agreeToTerms: json['agreeToTerms'] as bool,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'password': password,
      'confirmPassword': confirmPassword,
      'agreeToTerms': agreeToTerms,
      'firstName': firstName,
      'lastName': lastName,
    };
  }

  /// Create from entity
  factory RegisterRequestModel.fromEntity(RegisterRequest request) {
    return RegisterRequestModel(
      email: request.email,
      password: request.password,
      confirmPassword: request.confirmPassword,
      agreeToTerms: request.agreeToTerms,
      firstName: request.firstName,
      lastName: request.lastName,
    );
  }
}

/// Forgot password request model for data layer
class ForgotPasswordRequestModel extends ForgotPasswordRequest {
  const ForgotPasswordRequestModel({
    required super.email,
  });

  /// Create from JSON
  factory ForgotPasswordRequestModel.fromJson(Map<String, dynamic> json) {
    return ForgotPasswordRequestModel(
      email: json['email'] as String,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
    };
  }

  /// Create from entity
  factory ForgotPasswordRequestModel.fromEntity(ForgotPasswordRequest request) {
    return ForgotPasswordRequestModel(
      email: request.email,
    );
  }
}

/// Reset password request model for data layer
class ResetPasswordRequestModel extends ResetPasswordRequest {
  const ResetPasswordRequestModel({
    required super.email,
    required super.verificationCode,
    required super.newPassword,
    required super.confirmPassword,
  });

  /// Create from JSON
  factory ResetPasswordRequestModel.fromJson(Map<String, dynamic> json) {
    return ResetPasswordRequestModel(
      email: json['email'] as String,
      verificationCode: json['verificationCode'] as String,
      newPassword: json['newPassword'] as String,
      confirmPassword: json['confirmPassword'] as String,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'verificationCode': verificationCode,
      'newPassword': newPassword,
      'confirmPassword': confirmPassword,
    };
  }

  /// Create from entity
  factory ResetPasswordRequestModel.fromEntity(ResetPasswordRequest request) {
    return ResetPasswordRequestModel(
      email: request.email,
      verificationCode: request.verificationCode,
      newPassword: request.newPassword,
      confirmPassword: request.confirmPassword,
    );
  }
}
