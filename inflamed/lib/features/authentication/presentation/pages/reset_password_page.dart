import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_colors.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_form_validator.dart';

class ResetPasswordPage extends StatefulWidget {
  final String? email;
  
  const ResetPasswordPage({
    super.key,
    this.email,
  });

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> with AuthFormValidatorMixin {
  final _formKey = GlobalKey<FormState>();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _newPasswordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  
  bool _isLoading = false;
  String? _userEmail;

  @override
  void initState() {
    super.initState();
    
    // Set status bar style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
    
    // Get email from arguments or widget
    _userEmail = widget.email;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    
    // Get email from route arguments if not provided in widget
    if (_userEmail == null) {
      final args = ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
      _userEmail = args?['email'] as String?;
    }
  }

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _newPasswordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  void _handleResetPassword() async {
    // Validate all fields
    validatePassword('newPassword', _newPasswordController.text);
    validatePasswordConfirmation(
      'confirmPassword',
      _newPasswordController.text,
      _confirmPasswordController.text,
    );

    if (!isFormValid) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement actual reset password logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Password reset successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        
        // Navigate to login screen
        Navigator.pushNamedAndRemoveUntil(
          context,
          '/login',
          (route) => false,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to reset password: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToLogin() {
    Navigator.pushNamedAndRemoveUntil(
      context,
      '/login',
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Update status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
    );

    return AuthScreenWrapper(
      child: AuthLoadingOverlay(
        isLoading: _isLoading,
        loadingText: 'Resetting password...',
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Header with illustration
              AuthHeader(
                title: 'Reset password',
                subtitle: _userEmail != null 
                    ? 'Enter 4-digit recovery code we have sent to your email: ${_userEmail}'
                    : 'Enter your new password details',
                svgAssetPath: isDark 
                    ? 'assets/images/Authimages/resetDarkMode.svg'
                    : 'assets/images/Authimages/reset.svg',
                illustrationHeight: 180,
              ),
              
              const SizedBox(height: 40),
              
              // New password field
              AuthTextField(
                controller: _newPasswordController,
                focusNode: _newPasswordFocusNode,
                hintText: 'Enter your new password',
                isPassword: true,
                textInputAction: TextInputAction.next,
                errorText: getError('newPassword'),
                onChanged: (value) {
                  if (isTouched('newPassword')) {
                    validatePassword('newPassword', value);
                  }
                  // Also revalidate confirm password if it has been touched
                  if (isTouched('confirmPassword')) {
                    validatePasswordConfirmation(
                      'confirmPassword',
                      value,
                      _confirmPasswordController.text,
                    );
                  }
                },
                onFieldSubmitted: (_) {
                  _confirmPasswordFocusNode.requestFocus();
                },
                suffixIcon: Icon(
                  Icons.lock_outline,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Confirm password field
              AuthTextField(
                controller: _confirmPasswordController,
                focusNode: _confirmPasswordFocusNode,
                hintText: 'Confirm your password',
                isPassword: true,
                textInputAction: TextInputAction.done,
                errorText: getError('confirmPassword'),
                onChanged: (value) {
                  if (isTouched('confirmPassword')) {
                    validatePasswordConfirmation(
                      'confirmPassword',
                      _newPasswordController.text,
                      value,
                    );
                  }
                },
                onFieldSubmitted: (_) {
                  _handleResetPassword();
                },
                suffixIcon: Icon(
                  Icons.lock_outline,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Save button
              AuthButton.primary(
                text: 'SAVE',
                onPressed: _handleResetPassword,
                isLoading: _isLoading,
              ),
              
              const SizedBox(height: 24),
              
              // Login link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Not needed? ',
                    style: TextStyle(
                      color: isDark 
                          ? AppColors.secondaryTextDark 
                          : AppColors.secondaryText,
                      fontSize: 16,
                    ),
                  ),
                  AuthTextButton(
                    text: 'Login',
                    onPressed: _navigateToLogin,
                    textColor: isDark 
                        ? AppColors.primaryLinkTextDark 
                        : AppColors.primaryLinkText,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
