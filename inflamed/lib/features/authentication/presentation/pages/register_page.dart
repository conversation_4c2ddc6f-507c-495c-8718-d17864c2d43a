import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_colors.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_form_validator.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> with AuthFormValidatorMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  
  bool _isLoading = false;
  bool _agreeToTerms = false;

  @override
  void initState() {
    super.initState();
    
    // Set status bar style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  void _handleRegister() async {
    // Validate all fields
    validateEmail('email', _emailController.text);
    validatePassword('password', _passwordController.text);
    validatePasswordConfirmation(
      'confirmPassword',
      _passwordController.text,
      _confirmPasswordController.text,
    );

    // Validate terms agreement
    if (!_agreeToTerms) {
      setError('terms', 'You must agree to the terms and conditions');
    } else {
      clearError('terms');
    }

    if (!isFormValid || !_agreeToTerms) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement actual registration logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        // Show success message and navigate to login
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Registration successful! Please check your email to verify your account.'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pushReplacementNamed(context, '/login');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Registration failed: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToLogin() {
    Navigator.pushReplacementNamed(context, '/login');
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Update status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
    );

    return AuthScreenWrapper(
      child: AuthLoadingOverlay(
        isLoading: _isLoading,
        loadingText: 'Creating account...',
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Header with illustration
              AuthHeader(
                title: 'Welcome to\nContinua',
                subtitle: 'Create new account',
                svgAssetPath: isDark 
                    ? 'assets/images/Authimages/registerDarkMode.svg'
                    : 'assets/images/Authimages/register.svg',
                illustrationHeight: 180,
              ),
              
              const SizedBox(height: 40),
              
              // Email field
              AuthTextField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                hintText: 'Enter your email',
                isEmail: true,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                errorText: getError('email'),
                onChanged: (value) {
                  if (isTouched('email')) {
                    validateEmail('email', value);
                  }
                },
                onFieldSubmitted: (_) {
                  _passwordFocusNode.requestFocus();
                },
                suffixIcon: Icon(
                  Icons.email_outlined,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Password field
              AuthTextField(
                controller: _passwordController,
                focusNode: _passwordFocusNode,
                hintText: 'Enter your password',
                isPassword: true,
                textInputAction: TextInputAction.next,
                errorText: getError('password'),
                onChanged: (value) {
                  if (isTouched('password')) {
                    validatePassword('password', value);
                  }
                  // Also revalidate confirm password if it has been touched
                  if (isTouched('confirmPassword')) {
                    validatePasswordConfirmation(
                      'confirmPassword',
                      value,
                      _confirmPasswordController.text,
                    );
                  }
                },
                onFieldSubmitted: (_) {
                  _confirmPasswordFocusNode.requestFocus();
                },
                suffixIcon: Icon(
                  Icons.lock_outline,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Confirm password field
              AuthTextField(
                controller: _confirmPasswordController,
                focusNode: _confirmPasswordFocusNode,
                hintText: 'Confirm password',
                isPassword: true,
                textInputAction: TextInputAction.done,
                errorText: getError('confirmPassword'),
                onChanged: (value) {
                  if (isTouched('confirmPassword')) {
                    validatePasswordConfirmation(
                      'confirmPassword',
                      _passwordController.text,
                      value,
                    );
                  }
                },
                onFieldSubmitted: (_) {
                  _handleRegister();
                },
                suffixIcon: Icon(
                  Icons.lock_outline,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Terms and conditions checkbox
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Checkbox(
                    value: _agreeToTerms,
                    onChanged: (value) {
                      setState(() {
                        _agreeToTerms = value ?? false;
                      });
                      if (_agreeToTerms) {
                        clearError('terms');
                      }
                    },
                    activeColor: isDark ? AppColors.primaryDark : AppColors.primary,
                  ),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 12),
                      child: RichText(
                        text: TextSpan(
                          style: TextStyle(
                            color: isDark 
                                ? AppColors.secondaryTextDark 
                                : AppColors.secondaryText,
                            fontSize: 14,
                          ),
                          children: [
                            const TextSpan(text: 'I agree to '),
                            TextSpan(
                              text: 'terms',
                              style: TextStyle(
                                color: isDark 
                                    ? AppColors.primaryLinkTextDark 
                                    : AppColors.primaryLinkText,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            const TextSpan(text: ' and '),
                            TextSpan(
                              text: 'conditions',
                              style: TextStyle(
                                color: isDark 
                                    ? AppColors.primaryLinkTextDark 
                                    : AppColors.primaryLinkText,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                            const TextSpan(text: ' and '),
                            TextSpan(
                              text: 'privacy policy',
                              style: TextStyle(
                                color: isDark 
                                    ? AppColors.primaryLinkTextDark 
                                    : AppColors.primaryLinkText,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              // Terms error
              if (getError('terms') != null) ...[
                const SizedBox(height: 8),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 16),
                    child: Text(
                      getError('terms')!,
                      style: TextStyle(
                        color: isDark ? AppColors.errorDark : AppColors.error,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ],
              
              const SizedBox(height: 32),
              
              // Register button
              AuthButton.primary(
                text: 'REGISTER',
                onPressed: _handleRegister,
                isLoading: _isLoading,
              ),
              
              const SizedBox(height: 24),
              
              // Login link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Already have an account? ',
                    style: TextStyle(
                      color: isDark 
                          ? AppColors.secondaryTextDark 
                          : AppColors.secondaryText,
                      fontSize: 16,
                    ),
                  ),
                  AuthTextButton(
                    text: 'Login',
                    onPressed: _navigateToLogin,
                    textColor: isDark 
                        ? AppColors.primaryLinkTextDark 
                        : AppColors.primaryLinkText,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
