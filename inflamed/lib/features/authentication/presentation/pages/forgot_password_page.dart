import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../core/constants/app_colors.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_form_validator.dart';

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> with AuthFormValidatorMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _emailFocusNode = FocusNode();
  
  bool _isLoading = false;
  bool _codeSent = false;

  @override
  void initState() {
    super.initState();
    
    // Set status bar style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _emailFocusNode.dispose();
    super.dispose();
  }

  void _handleSendCode() async {
    // Validate email field
    validateEmail('email', _emailController.text);

    if (!isFormValid) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Implement actual forgot password logic
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (mounted) {
        setState(() {
          _codeSent = true;
        });
        
        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Verification code sent to ${_emailController.text}'),
            backgroundColor: Colors.green,
          ),
        );
        
        // Navigate to reset password screen with email
        Navigator.pushReplacementNamed(
          context, 
          '/reset-password',
          arguments: {'email': _emailController.text},
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send code: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _navigateToLogin() {
    Navigator.pushReplacementNamed(context, '/login');
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Update status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
    );

    return AuthScreenWrapper(
      child: AuthLoadingOverlay(
        isLoading: _isLoading,
        loadingText: 'Sending verification code...',
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Header with illustration
              AuthHeader(
                title: 'Forgot password',
                subtitle: 'Please enter your registered email address or phone number so we\'ll send you a verification code to reset your password.',
                svgAssetPath: isDark 
                    ? 'assets/images/Authimages/forgotDarkMode.svg'
                    : 'assets/images/Authimages/forgot.svg',
                illustrationHeight: 180,
              ),
              
              const SizedBox(height: 40),
              
              // Email field
              AuthTextField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                hintText: 'Enter your email',
                isEmail: true,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.done,
                errorText: getError('email'),
                onChanged: (value) {
                  if (isTouched('email')) {
                    validateEmail('email', value);
                  }
                },
                onFieldSubmitted: (_) {
                  _handleSendCode();
                },
                suffixIcon: Icon(
                  Icons.email_outlined,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Send code button
              AuthButton.primary(
                text: 'SEND CODE',
                onPressed: _handleSendCode,
                isLoading: _isLoading,
              ),
              
              const SizedBox(height: 24),
              
              // Login link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Not needed? ',
                    style: TextStyle(
                      color: isDark 
                          ? AppColors.secondaryTextDark 
                          : AppColors.secondaryText,
                      fontSize: 16,
                    ),
                  ),
                  AuthTextButton(
                    text: 'Login',
                    onPressed: _navigateToLogin,
                    textColor: isDark 
                        ? AppColors.primaryLinkTextDark 
                        : AppColors.primaryLinkText,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
