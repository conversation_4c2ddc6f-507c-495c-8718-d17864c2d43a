import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/app_colors.dart';
import '../../domain/entities/auth_request.dart';
import '../bloc/auth_bloc.dart';
import '../bloc/auth_event.dart';
import '../bloc/auth_state.dart';
import '../widgets/auth_header.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_form_validator.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with AuthFormValidatorMixin {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    
    // Set status bar style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  void _handleLogin() {
    // Validate all fields
    validateEmail('email', _emailController.text);
    validatePassword('password', _passwordController.text);

    if (!isFormValid) {
      return;
    }

    // Create login request and dispatch to BLoC
    final loginRequest = LoginRequest(
      email: _emailController.text.trim(),
      password: _passwordController.text,
    );

    context.read<AuthBloc>().add(LoginRequested(loginRequest));
  }

  void _navigateToRegister() {
    Navigator.pushNamed(context, '/register');
  }

  void _navigateToForgotPassword() {
    Navigator.pushNamed(context, '/forgot-password');
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Update status bar based on theme
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
      ),
    );

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAuthenticated) {
          // Navigate to home on successful login
          Navigator.pushReplacementNamed(context, '/home');
        } else if (state is AuthError) {
          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.failure.message),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      },
      child: BlocBuilder<AuthBloc, AuthState>(
        builder: (context, state) {
          final isLoading = state is AuthLoading;

          return AuthScreenWrapper(
            child: AuthLoadingOverlay(
              isLoading: isLoading,
              loadingText: state is AuthLoading ? state.message : 'Signing in...',
              child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: 40),
              
              // Header with illustration
              AuthHeader(
                title: 'Welcome back!',
                subtitle: 'Enter your login details',
                svgAssetPath: isDark 
                    ? 'assets/images/Authimages/loginDarkMode.svg'
                    : 'assets/images/Authimages/login.svg',
                illustrationHeight: 180,
              ),
              
              const SizedBox(height: 40),
              
              // Email field
              AuthTextField(
                controller: _emailController,
                focusNode: _emailFocusNode,
                hintText: 'Enter your email',
                isEmail: true,
                keyboardType: TextInputType.emailAddress,
                textInputAction: TextInputAction.next,
                errorText: getError('email'),
                onChanged: (value) {
                  if (isTouched('email')) {
                    validateEmail('email', value);
                  }
                },
                onFieldSubmitted: (_) {
                  _passwordFocusNode.requestFocus();
                },
                suffixIcon: Icon(
                  Icons.email_outlined,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 20),
              
              // Password field
              AuthTextField(
                controller: _passwordController,
                focusNode: _passwordFocusNode,
                hintText: 'Enter your password',
                isPassword: true,
                textInputAction: TextInputAction.done,
                errorText: getError('password'),
                onChanged: (value) {
                  if (isTouched('password')) {
                    validatePassword('password', value);
                  }
                },
                onFieldSubmitted: (_) {
                  _handleLogin();
                },
                suffixIcon: Icon(
                  Icons.lock_outline,
                  color: isDark 
                      ? AppColors.secondaryTextDark 
                      : AppColors.secondaryText,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Forgot password link
              Align(
                alignment: Alignment.centerRight,
                child: AuthTextButton(
                  text: 'Forgot password',
                  onPressed: _navigateToForgotPassword,
                  textColor: isDark 
                      ? AppColors.primaryLinkTextDark 
                      : AppColors.primaryLinkText,
                ),
              ),
              
              const SizedBox(height: 32),
              
              // Login button
              AuthButton.primary(
                text: 'LOGIN',
                onPressed: isLoading ? null : _handleLogin,
                isLoading: isLoading,
              ),
              
              const SizedBox(height: 24),
              
              // Register link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Not a member? ',
                    style: TextStyle(
                      color: isDark 
                          ? AppColors.secondaryTextDark 
                          : AppColors.secondaryText,
                      fontSize: 16,
                    ),
                  ),
                  AuthTextButton(
                    text: 'Register now',
                    onPressed: _navigateToRegister,
                    textColor: isDark 
                        ? AppColors.primaryLinkTextDark 
                        : AppColors.primaryLinkText,
                    fontWeight: FontWeight.w600,
                  ),
                ],
              ),
              
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
        },
      ),
    );
  }
}
