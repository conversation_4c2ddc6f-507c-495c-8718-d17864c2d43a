import 'package:equatable/equatable.dart';
import '../../domain/entities/user.dart';
import '../../domain/entities/auth_failure.dart';

/// Base class for authentication states
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

/// Initial authentication state
class AuthInitial extends AuthState {
  const AuthInitial();
}

/// Authentication loading state
class AuthLoading extends AuthState {
  final String? message;

  const AuthLoading({this.message});

  @override
  List<Object?> get props => [message];
}

/// User is authenticated
class AuthAuthenticated extends AuthState {
  final User user;
  final String accessToken;
  final DateTime expiresAt;

  const AuthAuthenticated({
    required this.user,
    required this.accessToken,
    required this.expiresAt,
  });

  /// Check if token is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if token will expire soon (within 5 minutes)
  bool get willExpireSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  @override
  List<Object?> get props => [user, accessToken, expiresAt];
}

/// User is not authenticated
class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

/// Authentication error state
class AuthError extends AuthState {
  final AuthFailure failure;

  const AuthError(this.failure);

  @override
  List<Object?> get props => [failure];
}

/// Registration successful but email verification required
class AuthRegistrationSuccess extends AuthState {
  final User user;
  final String message;

  const AuthRegistrationSuccess({
    required this.user,
    required this.message,
  });

  @override
  List<Object?> get props => [user, message];
}

/// Password reset code sent successfully
class AuthPasswordResetCodeSent extends AuthState {
  final String email;
  final String message;

  const AuthPasswordResetCodeSent({
    required this.email,
    required this.message,
  });

  @override
  List<Object?> get props => [email, message];
}

/// Password reset successful
class AuthPasswordResetSuccess extends AuthState {
  final String message;

  const AuthPasswordResetSuccess(this.message);

  @override
  List<Object?> get props => [message];
}

/// Email verification successful
class AuthEmailVerificationSuccess extends AuthState {
  final User user;
  final String message;

  const AuthEmailVerificationSuccess({
    required this.user,
    required this.message,
  });

  @override
  List<Object?> get props => [user, message];
}

/// Verification email sent
class AuthVerificationEmailSent extends AuthState {
  final String message;

  const AuthVerificationEmailSent(this.message);

  @override
  List<Object?> get props => [message];
}

/// Token refresh successful
class AuthTokenRefreshed extends AuthState {
  final User user;
  final String accessToken;
  final DateTime expiresAt;

  const AuthTokenRefreshed({
    required this.user,
    required this.accessToken,
    required this.expiresAt,
  });

  @override
  List<Object?> get props => [user, accessToken, expiresAt];
}
