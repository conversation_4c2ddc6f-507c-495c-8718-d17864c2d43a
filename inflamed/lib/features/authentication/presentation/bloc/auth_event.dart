import 'package:equatable/equatable.dart';
import '../../domain/entities/auth_request.dart';

/// Base class for authentication events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

/// Event to initialize authentication state
class AuthInitialized extends AuthEvent {
  const AuthInitialized();
}

/// Event to check authentication status
class AuthStatusChecked extends AuthEvent {
  const AuthStatusChecked();
}

/// Event to login user
class LoginRequested extends AuthEvent {
  final LoginRequest request;

  const LoginRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event to register user
class RegisterRequested extends AuthEvent {
  final RegisterRequest request;

  const RegisterRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event to request password reset
class ForgotPasswordRequested extends AuthEvent {
  final ForgotPasswordRequest request;

  const ForgotPasswordRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event to reset password
class ResetPasswordRequested extends AuthEvent {
  final ResetPasswordRequest request;

  const ResetPasswordRequested(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event to logout user
class LogoutRequested extends AuthEvent {
  const LogoutRequested();
}

/// Event to refresh authentication token
class TokenRefreshRequested extends AuthEvent {
  const TokenRefreshRequested();
}

/// Event to verify email
class EmailVerificationRequested extends AuthEvent {
  final String verificationCode;

  const EmailVerificationRequested(this.verificationCode);

  @override
  List<Object?> get props => [verificationCode];
}

/// Event to resend verification email
class ResendVerificationRequested extends AuthEvent {
  const ResendVerificationRequested();
}

/// Event to clear authentication errors
class AuthErrorCleared extends AuthEvent {
  const AuthErrorCleared();
}
