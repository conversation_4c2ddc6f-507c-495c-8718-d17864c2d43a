import 'package:flutter/material.dart';
import '../../../../core/constants/app_colors.dart';

/// Custom button for authentication screens
class AuthButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isSecondary;
  final Widget? icon;
  final double? width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;

  const AuthButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isSecondary = false,
    this.icon,
    this.width,
    this.height = 56,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.textColor,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
  });

  /// Primary button constructor
  const AuthButton.primary({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height = 56,
    this.padding,
    this.borderRadius,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
  }) : isSecondary = false,
       backgroundColor = null,
       textColor = null;

  /// Secondary button constructor
  const AuthButton.secondary({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height = 56,
    this.padding,
    this.borderRadius,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w600,
  }) : isSecondary = true,
       backgroundColor = null,
       textColor = null;

  /// Text button constructor
  const AuthButton.text({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.icon,
    this.width,
    this.height = 48,
    this.padding,
    this.borderRadius,
    this.backgroundColor,
    this.textColor,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w500,
  }) : isSecondary = false;

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Determine colors based on theme and button type
    Color getBackgroundColor() {
      if (backgroundColor != null) return backgroundColor!;
      
      if (isSecondary) {
        return isDark 
            ? AppColors.secondaryButtonBackgroundDark 
            : AppColors.secondaryButtonBackground;
      }
      
      return isDark 
          ? AppColors.primaryButtonBackgroundDark 
          : AppColors.primaryButtonBackground;
    }

    Color getTextColor() {
      if (textColor != null) return textColor!;
      
      if (isSecondary) {
        return isDark 
            ? AppColors.secondaryButtonTextDark 
            : AppColors.secondaryButtonText;
      }
      
      return isDark 
          ? AppColors.primaryButtonTextDark 
          : AppColors.primaryButtonText;
    }

    return SizedBox(
      width: width ?? double.infinity,
      height: height,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: getBackgroundColor(),
          foregroundColor: getTextColor(),
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(28),
            side: isSecondary 
                ? BorderSide(
                    color: isDark 
                        ? AppColors.themedBorderDark 
                        : AppColors.themedBorder,
                    width: 1,
                  )
                : BorderSide.none,
          ),
          padding: padding ?? const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 16,
          ),
          disabledBackgroundColor: getBackgroundColor().withValues(alpha: 0.6),
          disabledForegroundColor: getTextColor().withValues(alpha: 0.6),
        ),
        child: isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(getTextColor()),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (icon != null) ...[
                    icon!,
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: TextStyle(
                      fontSize: fontSize,
                      fontWeight: fontWeight,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}

/// Text button for authentication screens
class AuthTextButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final Color? textColor;
  final double fontSize;
  final FontWeight fontWeight;
  final TextDecoration? decoration;

  const AuthTextButton({
    super.key,
    required this.text,
    this.onPressed,
    this.textColor,
    this.fontSize = 16,
    this.fontWeight = FontWeight.w500,
    this.decoration,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: textColor ?? 
            (isDark ? AppColors.primaryLinkTextDark : AppColors.primaryLinkText),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        minimumSize: Size.zero,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: fontSize,
          fontWeight: fontWeight,
          decoration: decoration,
          color: textColor ?? 
              (isDark ? AppColors.primaryLinkTextDark : AppColors.primaryLinkText),
        ),
      ),
    );
  }
}
