import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../../core/constants/app_colors.dart';

/// Header widget for authentication screens with illustration and text
class AuthHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? svgAssetPath;
  final double? illustrationHeight;
  final EdgeInsetsGeometry? padding;

  const AuthHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.svgAssetPath,
    this.illustrationHeight = 200,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        children: [
          // Illustration
          if (svgAssetPath != null) ...[
            SizedBox(
              height: illustrationHeight,
              child: SvgPicture.asset(
                svgAssetPath!,
                fit: BoxFit.contain,
              ),
            ),
            const SizedBox(height: 32),
          ],
          
          // Title
          Text(
            title,
            style: Theme.of(context).textTheme.displaySmall?.copyWith(
              color: isDark ? AppColors.primaryTextDark : AppColors.primaryText,
              fontWeight: FontWeight.bold,
              fontSize: 28,
            ),
            textAlign: TextAlign.center,
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 12),
            Text(
              subtitle!,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isDark ? AppColors.secondaryTextDark : AppColors.secondaryText,
                fontSize: 16,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// Compact header for authentication screens
class AuthCompactHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final EdgeInsetsGeometry? padding;

  const AuthCompactHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Padding(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            title,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: isDark ? AppColors.primaryTextDark : AppColors.primaryText,
              fontWeight: FontWeight.bold,
              fontSize: 24,
            ),
          ),
          
          // Subtitle
          if (subtitle != null) ...[
            const SizedBox(height: 8),
            Text(
              subtitle!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: isDark ? AppColors.secondaryTextDark : AppColors.secondaryText,
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// Authentication screen wrapper with consistent styling
class AuthScreenWrapper extends StatelessWidget {
  final Widget child;
  final bool hasScrollableContent;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;

  const AuthScreenWrapper({
    super.key,
    required this.child,
    this.hasScrollableContent = true,
    this.padding,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: backgroundColor ?? 
          (isDark ? AppColors.primaryBackgroundDark : AppColors.primaryBackground),
      body: SafeArea(
        child: hasScrollableContent
            ? SingleChildScrollView(
                padding: padding ?? const EdgeInsets.all(24),
                child: child,
              )
            : Padding(
                padding: padding ?? const EdgeInsets.all(24),
                child: child,
              ),
      ),
    );
  }
}

/// Loading overlay for authentication screens
class AuthLoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final Widget child;
  final String? loadingText;

  const AuthLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingText,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: Colors.black.withValues(alpha: 0.3),
            child: Center(
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    if (loadingText != null) ...[
                      const SizedBox(height: 16),
                      Text(
                        loadingText!,
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}
