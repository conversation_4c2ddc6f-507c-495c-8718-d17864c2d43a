import 'package:flutter/material.dart';
import '../../domain/entities/validation_result.dart';

/// Form validation mixin for authentication forms
mixin AuthFormValidatorMixin<T extends StatefulWidget> on State<T> {
  final Map<String, String?> _errors = {};
  final Map<String, bool> _touched = {};

  /// Get error for a field
  String? getError(String fieldName) => _errors[fieldName];

  /// Check if field has been touched
  bool isTouched(String fieldName) => _touched[fieldName] ?? false;

  /// Set error for a field
  void setError(String fieldName, String? error) {
    setState(() {
      _errors[fieldName] = error;
    });
  }

  /// Mark field as touched
  void markTouched(String fieldName) {
    setState(() {
      _touched[fieldName] = true;
    });
  }

  /// Clear error for a field
  void clearError(String fieldName) {
    setState(() {
      _errors.remove(fieldName);
    });
  }

  /// Clear all errors
  void clearAllErrors() {
    setState(() {
      _errors.clear();
    });
  }

  /// Validate email field
  void validateEmail(String fieldName, String? value) {
    markTouched(fieldName);
    final result = EmailValidator.validate(value);
    setError(fieldName, result.isValid ? null : result.errorMessage);
  }

  /// Validate password field
  void validatePassword(String fieldName, String? value) {
    markTouched(fieldName);
    final result = PasswordValidator.validate(value);
    setError(fieldName, result.isValid ? null : result.errorMessage);
  }

  /// Validate password confirmation
  void validatePasswordConfirmation(
    String fieldName,
    String? password,
    String? confirmPassword,
  ) {
    markTouched(fieldName);
    final result = PasswordValidator.validateConfirmation(password, confirmPassword);
    setError(fieldName, result.isValid ? null : result.errorMessage);
  }

  /// Validate name field
  void validateName(String fieldName, String? value, String displayName) {
    markTouched(fieldName);
    final result = NameValidator.validate(value, fieldName: displayName);
    setError(fieldName, result.isValid ? null : result.errorMessage);
  }

  /// Validate verification code
  void validateVerificationCode(String fieldName, String? value) {
    markTouched(fieldName);
    final result = VerificationCodeValidator.validate(value);
    setError(fieldName, result.isValid ? null : result.errorMessage);
  }

  /// Check if form is valid
  bool get isFormValid => _errors.values.every((error) => error == null);

  /// Get all current errors
  Map<String, String?> get allErrors => Map.from(_errors);
}

/// Custom form field with validation
class ValidatedFormField extends StatefulWidget {
  final String fieldName;
  final String? label;
  final String? hintText;
  final bool isPassword;
  final bool isEmail;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final ValueChanged<String>? onChanged;
  final TextInputType? keyboardType;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final bool enabled;
  final int maxLines;
  final TextInputAction? textInputAction;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onFieldSubmitted;

  const ValidatedFormField({
    super.key,
    required this.fieldName,
    this.label,
    this.hintText,
    this.isPassword = false,
    this.isEmail = false,
    this.controller,
    this.validator,
    this.onChanged,
    this.keyboardType,
    this.suffixIcon,
    this.prefixIcon,
    this.enabled = true,
    this.maxLines = 1,
    this.textInputAction,
    this.onEditingComplete,
    this.onFieldSubmitted,
  });

  @override
  State<ValidatedFormField> createState() => _ValidatedFormFieldState();
}

class _ValidatedFormFieldState extends State<ValidatedFormField> {
  bool _obscureText = true;
  String? _errorText;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
        ],
        TextFormField(
          controller: widget.controller,
          obscureText: widget.isPassword ? _obscureText : false,
          keyboardType: widget.keyboardType ?? 
              (widget.isEmail ? TextInputType.emailAddress : TextInputType.text),
          textInputAction: widget.textInputAction,
          maxLines: widget.maxLines,
          enabled: widget.enabled,
          validator: widget.validator,
          onChanged: (value) {
            // Clear error when user starts typing
            if (_errorText != null) {
              setState(() {
                _errorText = null;
              });
            }
            widget.onChanged?.call(value);
          },
          onEditingComplete: widget.onEditingComplete,
          onFieldSubmitted: widget.onFieldSubmitted,
          decoration: InputDecoration(
            hintText: widget.hintText,
            errorText: _errorText,
            filled: true,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide.none,
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            prefixIcon: widget.prefixIcon,
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _obscureText ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () {
                      setState(() {
                        _obscureText = !_obscureText;
                      });
                    },
                  )
                : widget.suffixIcon,
          ),
        ),
      ],
    );
  }

  /// Set error text from parent
  void setError(String? error) {
    setState(() {
      _errorText = error;
    });
  }
}
