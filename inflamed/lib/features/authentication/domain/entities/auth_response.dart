import 'package:equatable/equatable.dart';
import 'user.dart';

/// Base class for authentication responses
abstract class AuthResponse extends Equatable {
  const AuthResponse();
}

/// Login response entity
class LoginResponse extends AuthResponse {
  final User user;
  final String accessToken;
  final String refreshToken;
  final DateTime expiresAt;

  const LoginResponse({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
    required this.expiresAt,
  });

  /// Check if token is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  /// Check if token will expire soon (within 5 minutes)
  bool get willExpireSoon {
    final fiveMinutesFromNow = DateTime.now().add(const Duration(minutes: 5));
    return fiveMinutesFromNow.isAfter(expiresAt);
  }

  @override
  List<Object?> get props => [user, accessToken, refreshToken, expiresAt];

  @override
  String toString() => 'LoginResponse(user: ${user.email}, expiresAt: $expiresAt)';
}

/// Register response entity
class RegisterResponse extends AuthResponse {
  final User user;
  final String? accessToken;
  final String? refreshToken;
  final DateTime? expiresAt;
  final bool requiresEmailVerification;
  final String? verificationMessage;

  const RegisterResponse({
    required this.user,
    this.accessToken,
    this.refreshToken,
    this.expiresAt,
    this.requiresEmailVerification = true,
    this.verificationMessage,
  });

  @override
  List<Object?> get props => [
        user,
        accessToken,
        refreshToken,
        expiresAt,
        requiresEmailVerification,
        verificationMessage,
      ];

  @override
  String toString() => 'RegisterResponse(user: ${user.email}, requiresEmailVerification: $requiresEmailVerification)';
}

/// Forgot password response entity
class ForgotPasswordResponse extends AuthResponse {
  final String message;
  final String? verificationCodeSentTo;
  final DateTime? codeExpiresAt;

  const ForgotPasswordResponse({
    required this.message,
    this.verificationCodeSentTo,
    this.codeExpiresAt,
  });

  @override
  List<Object?> get props => [message, verificationCodeSentTo, codeExpiresAt];

  @override
  String toString() => 'ForgotPasswordResponse(message: $message)';
}

/// Reset password response entity
class ResetPasswordResponse extends AuthResponse {
  final String message;
  final bool success;

  const ResetPasswordResponse({
    required this.message,
    required this.success,
  });

  @override
  List<Object?> get props => [message, success];

  @override
  String toString() => 'ResetPasswordResponse(message: $message, success: $success)';
}

/// Generic auth success response
class AuthSuccessResponse extends AuthResponse {
  final String message;
  final Map<String, dynamic>? data;

  const AuthSuccessResponse({
    required this.message,
    this.data,
  });

  @override
  List<Object?> get props => [message, data];

  @override
  String toString() => 'AuthSuccessResponse(message: $message)';
}
