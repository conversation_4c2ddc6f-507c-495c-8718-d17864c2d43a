import 'package:equatable/equatable.dart';

/// Validation result entity
class ValidationResult extends Equatable {
  final bool isValid;
  final String? errorMessage;
  final List<String> errors;

  const ValidationResult({
    required this.isValid,
    this.errorMessage,
    this.errors = const [],
  });

  /// Create a valid result
  factory ValidationResult.valid() {
    return const ValidationResult(isValid: true);
  }

  /// Create an invalid result with a single error
  factory ValidationResult.invalid(String errorMessage) {
    return ValidationResult(
      isValid: false,
      errorMessage: errorMessage,
      errors: [errorMessage],
    );
  }

  /// Create an invalid result with multiple errors
  factory ValidationResult.invalidWithErrors(List<String> errors) {
    return ValidationResult(
      isValid: false,
      errorMessage: errors.isNotEmpty ? errors.first : null,
      errors: errors,
    );
  }

  @override
  List<Object?> get props => [isValid, errorMessage, errors];

  @override
  String toString() => 'ValidationResult(isValid: $isValid, errorMessage: $errorMessage)';
}

/// Email validation utility
class EmailValidator {
  static const String _emailPattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static final RegExp _emailRegex = RegExp(_emailPattern);

  static ValidationResult validate(String? email) {
    if (email == null || email.isEmpty) {
      return ValidationResult.invalid('Email is required');
    }

    if (!_emailRegex.hasMatch(email)) {
      return ValidationResult.invalid('Please enter a valid email address');
    }

    return ValidationResult.valid();
  }
}

/// Password validation utility
class PasswordValidator {
  static const int minLength = 8;
  static const int maxLength = 128;

  static ValidationResult validate(String? password) {
    if (password == null || password.isEmpty) {
      return ValidationResult.invalid('Password is required');
    }

    final errors = <String>[];

    if (password.length < minLength) {
      errors.add('Password must be at least $minLength characters long');
    }

    if (password.length > maxLength) {
      errors.add('Password must be less than $maxLength characters long');
    }

    if (!password.contains(RegExp(r'[A-Z]'))) {
      errors.add('Password must contain at least one uppercase letter');
    }

    if (!password.contains(RegExp(r'[a-z]'))) {
      errors.add('Password must contain at least one lowercase letter');
    }

    if (!password.contains(RegExp(r'[0-9]'))) {
      errors.add('Password must contain at least one number');
    }

    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      errors.add('Password must contain at least one special character');
    }

    if (errors.isEmpty) {
      return ValidationResult.valid();
    }

    return ValidationResult.invalidWithErrors(errors);
  }

  /// Validate password confirmation
  static ValidationResult validateConfirmation(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return ValidationResult.invalid('Please confirm your password');
    }

    if (password != confirmPassword) {
      return ValidationResult.invalid('Passwords do not match');
    }

    return ValidationResult.valid();
  }
}

/// Name validation utility
class NameValidator {
  static const int minLength = 2;
  static const int maxLength = 50;

  static ValidationResult validate(String? name, {required String fieldName}) {
    if (name == null || name.isEmpty) {
      return ValidationResult.invalid('$fieldName is required');
    }

    if (name.length < minLength) {
      return ValidationResult.invalid('$fieldName must be at least $minLength characters long');
    }

    if (name.length > maxLength) {
      return ValidationResult.invalid('$fieldName must be less than $maxLength characters long');
    }

    if (!RegExp(r'^[a-zA-Z\s]+$').hasMatch(name)) {
      return ValidationResult.invalid('$fieldName can only contain letters and spaces');
    }

    return ValidationResult.valid();
  }
}

/// Verification code validation utility
class VerificationCodeValidator {
  static const int codeLength = 4;

  static ValidationResult validate(String? code) {
    if (code == null || code.isEmpty) {
      return ValidationResult.invalid('Verification code is required');
    }

    if (code.length != codeLength) {
      return ValidationResult.invalid('Verification code must be $codeLength digits');
    }

    if (!RegExp(r'^[0-9]+$').hasMatch(code)) {
      return ValidationResult.invalid('Verification code can only contain numbers');
    }

    return ValidationResult.valid();
  }
}
