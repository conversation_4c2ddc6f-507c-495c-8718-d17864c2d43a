import 'package:equatable/equatable.dart';

/// Authentication failure entity
class AuthFailure extends Equatable {
  final String message;
  final String? code;
  final Map<String, dynamic>? details;
  final AuthFailureType type;

  const AuthFailure({
    required this.message,
    this.code,
    this.details,
    this.type = AuthFailureType.unknown,
  });

  /// Factory constructors for common failures
  factory AuthFailure.invalidCredentials([String? message]) {
    return AuthFailure(
      message: message ?? 'Invalid email or password',
      type: AuthFailureType.invalidCredentials,
      code: 'INVALID_CREDENTIALS',
    );
  }

  factory AuthFailure.userNotFound([String? message]) {
    return AuthFailure(
      message: message ?? 'User not found',
      type: AuthFailureType.userNotFound,
      code: 'USER_NOT_FOUND',
    );
  }

  factory AuthFailure.emailAlreadyExists([String? message]) {
    return AuthFailure(
      message: message ?? 'Email already exists',
      type: AuthFailureType.emailAlreadyExists,
      code: 'EMAIL_ALREADY_EXISTS',
    );
  }

  factory AuthFailure.weakPassword([String? message]) {
    return AuthFailure(
      message: message ?? 'Password is too weak',
      type: AuthFailureType.weakPassword,
      code: 'WEAK_PASSWORD',
    );
  }

  factory AuthFailure.invalidEmail([String? message]) {
    return AuthFailure(
      message: message ?? 'Invalid email format',
      type: AuthFailureType.invalidEmail,
      code: 'INVALID_EMAIL',
    );
  }

  factory AuthFailure.networkError([String? message]) {
    return AuthFailure(
      message: message ?? 'Network connection error',
      type: AuthFailureType.networkError,
      code: 'NETWORK_ERROR',
    );
  }

  factory AuthFailure.serverError([String? message]) {
    return AuthFailure(
      message: message ?? 'Server error occurred',
      type: AuthFailureType.serverError,
      code: 'SERVER_ERROR',
    );
  }

  factory AuthFailure.tokenExpired([String? message]) {
    return AuthFailure(
      message: message ?? 'Session expired',
      type: AuthFailureType.tokenExpired,
      code: 'TOKEN_EXPIRED',
    );
  }

  factory AuthFailure.invalidVerificationCode([String? message]) {
    return AuthFailure(
      message: message ?? 'Invalid verification code',
      type: AuthFailureType.invalidVerificationCode,
      code: 'INVALID_VERIFICATION_CODE',
    );
  }

  factory AuthFailure.verificationCodeExpired([String? message]) {
    return AuthFailure(
      message: message ?? 'Verification code expired',
      type: AuthFailureType.verificationCodeExpired,
      code: 'VERIFICATION_CODE_EXPIRED',
    );
  }

  factory AuthFailure.unknown([String? message]) {
    return AuthFailure(
      message: message ?? 'An unknown error occurred',
      type: AuthFailureType.unknown,
      code: 'UNKNOWN_ERROR',
    );
  }

  @override
  List<Object?> get props => [message, code, details, type];

  @override
  String toString() => 'AuthFailure(message: $message, code: $code, type: $type)';
}

/// Types of authentication failures
enum AuthFailureType {
  invalidCredentials,
  userNotFound,
  emailAlreadyExists,
  weakPassword,
  invalidEmail,
  networkError,
  serverError,
  tokenExpired,
  invalidVerificationCode,
  verificationCodeExpired,
  unknown,
}
