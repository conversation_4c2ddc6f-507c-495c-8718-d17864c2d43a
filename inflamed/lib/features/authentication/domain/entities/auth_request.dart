import 'package:equatable/equatable.dart';

/// Base class for authentication requests
abstract class AuthRequest extends Equatable {
  const AuthRequest();
}

/// Login request entity
class LoginRequest extends AuthRequest {
  final String email;
  final String password;

  const LoginRequest({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];

  @override
  String toString() => 'LoginRequest(email: $email)';
}

/// Register request entity
class RegisterRequest extends AuthRequest {
  final String email;
  final String password;
  final String confirmPassword;
  final bool agreeToTerms;
  final String? firstName;
  final String? lastName;

  const RegisterRequest({
    required this.email,
    required this.password,
    required this.confirmPassword,
    required this.agreeToTerms,
    this.firstName,
    this.lastName,
  });

  @override
  List<Object?> get props => [
        email,
        password,
        confirmPassword,
        agreeToTerms,
        firstName,
        lastName,
      ];

  @override
  String toString() => 'RegisterRequest(email: $email, agreeToTerms: $agreeToTerms)';
}

/// Forgot password request entity
class ForgotPasswordRequest extends AuthRequest {
  final String email;

  const ForgotPasswordRequest({
    required this.email,
  });

  @override
  List<Object?> get props => [email];

  @override
  String toString() => 'ForgotPasswordRequest(email: $email)';
}

/// Reset password request entity
class ResetPasswordRequest extends AuthRequest {
  final String email;
  final String verificationCode;
  final String newPassword;
  final String confirmPassword;

  const ResetPasswordRequest({
    required this.email,
    required this.verificationCode,
    required this.newPassword,
    required this.confirmPassword,
  });

  @override
  List<Object?> get props => [
        email,
        verificationCode,
        newPassword,
        confirmPassword,
      ];

  @override
  String toString() => 'ResetPasswordRequest(email: $email)';
}

/// Change password request entity
class ChangePasswordRequest extends AuthRequest {
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
    required this.confirmPassword,
  });

  @override
  List<Object?> get props => [
        currentPassword,
        newPassword,
        confirmPassword,
      ];

  @override
  String toString() => 'ChangePasswordRequest()';
}
