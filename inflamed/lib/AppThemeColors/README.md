# AppThemeColors - iOS to Flutter Conversion

This directory contains the original iOS color set definitions that have been converted to <PERSON>lut<PERSON>'s color system.

## Conversion Summary

The iOS color sets have been successfully converted to Flutter and are now available in:
- `lib/core/constants/app_colors.dart` - All color definitions
- `lib/core/theme/app_theme.dart` - Theme configuration using the colors

## Color Categories Converted

### 1. Universal Colors
- `universalAccentColor` → `AppColors.universalAccent`
- `tabAccentColor` → `AppColors.tabAccent` / `AppColors.tabAccentDark`
- `themedBorderColor` → `AppColors.themedBorder` / `AppColors.themedBorderDark`
- `shadowColor` → `AppColors.shadow` / `AppColors.shadowDark`

### 2. Background Colors
- `primaryBackgroundColor` → `AppColors.primaryBackground` / `AppColors.primaryBackgroundDark`
- `secondaryBackgroundColor` → `AppColors.secondaryBackground` / `AppColors.secondaryBackgroundDark`
- `onboardingTutorialBackgroundColor` → `AppColors.onboardingBackground` / `AppColors.onboardingBackgroundDark`
- `splashScreenGradientTopLeft` → `AppColors.splashGradientTopLeft`
- `splashScreenGradientBottomRight` → `AppColors.splashGradientBottomRight`
- `airQualityRecordBackgroundColor` → `AppColors.airQualityBackground` / `AppColors.airQualityBackgroundDark`
- `recentNotiBackgroundColor` → `AppColors.recentNotiBackground` / `AppColors.recentNotiBackgroundDark`
- `highContrastTextBackgroundColor` → `AppColors.highContrastTextBackground` / `AppColors.highContrastTextBackgroundDark`

### 3. Text Colors
- `primaryTextColor` → `AppColors.primaryText` / `AppColors.primaryTextDark`
- `secondaryTextColor` → `AppColors.secondaryText` / `AppColors.secondaryTextDark`
- `primarySubtitleTextColor` → `AppColors.primarySubtitleText` / `AppColors.primarySubtitleTextDark`
- `onboardingTutorialText` → `AppColors.onboardingTutorialText` / `AppColors.onboardingTutorialTextDark`
- `urgentColor` → `AppColors.urgent` / `AppColors.urgentDark`

### 4. Button Colors
- `primaryButtonBackgroundColor` → `AppColors.primaryButtonBackground` / `AppColors.primaryButtonBackgroundDark`
- `secondaryButtonBackgroundColor` → `AppColors.secondaryButtonBackground` / `AppColors.secondaryButtonBackgroundDark`
- `tertiaryButtonBackgroundColor` → `AppColors.tertiaryButtonBackground` / `AppColors.tertiaryButtonBackgroundDark`
- `onboardingButtonBackgroundColor` → `AppColors.onboardingButtonBackground`
- `actionRequiredBackgroundColor` → `AppColors.actionRequiredButtonBackground` / `AppColors.actionRequiredButtonBackgroundDark`
- `nextAppointmentBackgroundColor` → `AppColors.nextAppointmentButtonBackground` / `AppColors.nextAppointmentButtonBackgroundDark`

### 5. Button Text Colors
- `primaryButtonTextColor` → `AppColors.primaryButtonText` / `AppColors.primaryButtonTextDark`
- `secondaryButtonTextColor` → `AppColors.secondaryButtonText` / `AppColors.secondaryButtonTextDark`
- `tertiaryButtonTextColor` → `AppColors.tertiaryButtonText` / `AppColors.tertiaryButtonTextDark`
- `onboardingButtonTextColor` → `AppColors.onboardingButtonText`

### 6. Link Text Colors
- `primaryLinkTextColor` → `AppColors.primaryLinkText` / `AppColors.primaryLinkTextDark`
- `secondaryLinkTextColor` → `AppColors.secondaryLinkText` / `AppColors.secondaryLinkTextDark`
- `onboardingLinkTextColor` → `AppColors.onboardingLinkText` / `AppColors.onboardingLinkTextDark`
- `universalLinkPinkColor` → `AppColors.universalLinkPink` / `AppColors.universalLinkPinkDark`

### 7. Text Field Colors
- `primaryTextFieldBackgroundColor` → `AppColors.primaryTextFieldBackground` / `AppColors.primaryTextFieldBackgroundDark`
- `secondaryTextFieldBackgroundColor` → `AppColors.secondaryTextFieldBackground` / `AppColors.secondaryTextFieldBackgroundDark`
- `primaryTextFieldTextColor` → `AppColors.primaryTextFieldText` / `AppColors.primaryTextFieldTextDark`
- `secondaryTextFieldTextColor` → `AppColors.secondaryTextFieldText` / `AppColors.secondaryTextFieldTextDark`

### 8. Body Severity Colors
- `SeverityNone` → `AppColors.severityNone` / `AppColors.severityNoneDark`
- `SeverityMild` → `AppColors.severityMild` / `AppColors.severityMildDark`
- `SeverityModerate` → `AppColors.severityModerate` / `AppColors.severityModerateDark`
- `SeveritySevere` → `AppColors.severitySevere` / `AppColors.severitySevereDark`

### 9. Air Quality Index Colors
- `aqiGood` → `AppColors.aqiGood` / `AppColors.aqiGoodDark`
- `aqiModerate` → `AppColors.aqiModerate` / `AppColors.aqiModerateDark`
- `aqiUnhealthyForSensitiveGroup` → `AppColors.aqiUnhealthyForSensitive` / `AppColors.aqiUnhealthyForSensitiveDark`
- `aqiUnhealthy` → `AppColors.aqiUnhealthy` / `AppColors.aqiUnhealthyDark`
- `aqiVeryUnhealthy` → `AppColors.aqiVeryUnhealthy` / `AppColors.aqiVeryUnhealthyDark`
- `aqiHazardous` → `AppColors.aqiHazardous` / `AppColors.aqiHazardousDark`

### 10. Chart Category Colors
- `happiness` → `AppColors.chartHappiness` / `AppColors.chartHappinessDark`
- `hrv` → `AppColors.chartHRV` / `AppColors.chartHRVDark`
- `sentiment` → `AppColors.chartSentiment` / `AppColors.chartSentimentDark`
- `sleep` → `AppColors.chartSleep` / `AppColors.chartSleepDark`
- `steps` → `AppColors.chartSteps` / `AppColors.chartStepsDark`
- `stress` → `AppColors.chartStress` / `AppColors.chartStressDark`

### 11. Semantic Colors (Convenience)
- `AppColors.primary` / `AppColors.primaryDark` - Main brand color
- `AppColors.secondary` / `AppColors.secondaryDark` - Secondary brand color
- `AppColors.success` / `AppColors.successDark` - Success states
- `AppColors.warning` / `AppColors.warningDark` - Warning states
- `AppColors.error` / `AppColors.errorDark` - Error states
- `AppColors.info` / `AppColors.infoDark` - Info states

## Usage in Flutter

```dart
import 'package:inflamed/core/constants/app_colors.dart';
import 'package:inflamed/core/theme/app_theme.dart';

// Using colors directly
Container(
  color: AppColors.primaryBackground,
  child: Text(
    'Hello World',
    style: TextStyle(color: AppColors.primaryText),
  ),
)

// Using theme
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
  // ...
)

// Accessing theme colors
Container(
  color: Theme.of(context).colorScheme.primary,
  child: Text(
    'Themed Text',
    style: Theme.of(context).textTheme.bodyLarge,
  ),
)
```

## Color Format Conversion

iOS color formats were converted as follows:
- Hex values (0xFF) → `Color.fromRGBO(r, g, b, 1.0)`
- Float values (0.0-1.0) → Converted to 0-255 range
- Both light and dark variants preserved

## Notes

- All colors support both light and dark themes
- Colors are organized by category for easy maintenance
- Semantic color aliases provided for common use cases
- Original iOS color set structure preserved for reference
- Colors are defined as static constants for performance
